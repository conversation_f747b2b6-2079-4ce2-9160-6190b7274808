import type {
  AxiosRequestConfig as AxiosRequestConfigOrigin,
  AxiosResponse,
} from "axios";
import type { ChunkData } from "./servers/chat/type";
import { parseStreamingData } from "@/subpages/chat-ai/composations/useParseStreaming";
import { TextDecoder2 } from "@/utils/TextDecoderEncoder";
// @ts-ignore
import settle from "axios/lib/core/settle.js";
// @ts-ignore
import buildURL from "axios/lib/helpers/buildURL.js";

export interface AxiosRequestConfig extends AxiosRequestConfigOrigin {
  signal?: AbortSignal;
}

export function adapter(
  config: AxiosRequestConfig,
): Promise<AxiosResponse<any>> {
  return new Promise((resolve, reject) => {
    console.log("adapter Config:", config);

    const options: UniApp.RequestOptions = {
      method: (config?.method?.toUpperCase() ??
        "GET") as UniApp.RequestOptions["method"],
      url: buildURL(config.url, config.params, config.paramsSerializer),
      header: config.headers,
      data: config.data,
      responseType: config.responseType,
      complete: (response: any) => {
        response = {
          data: response.data,
          status: response.statusCode,
          errMsg: response.errMsg,
          headers: response.header,
          config,
        };
        settle(resolve, reject, response);
      },
    };
    uni.request(options);
  });
}

export interface StreamResponse {
  data?: ChunkData;
  status?: "streaming" | "done";
}

export function streamAdapter(
  config: AxiosRequestConfig,
): Promise<AxiosResponse<any>> {
  return new Promise((resolve, reject) => {
    let chunkData: StreamResponse = {};
    let requestTask: WechatMiniprogram.RequestTask;
    const listener: WechatMiniprogram.OffChunkReceivedCallback = ({ data }) => {
      const decodeData = new TextDecoder2().decode(new Uint8Array(data));
      if (!decodeData) return;
      console.log("decodeData:", decodeData);

      const parseData: ChunkData[] = parseStreamingData(decodeData);
      parseData.forEach(item => {
        const response: StreamResponse = {
          data: item,
          status: "streaming",
        };
        chunkData = response;
        if (Array.isArray(config.transformResponse)) {
          config.transformResponse.forEach(fn => fn(response));
        } else {
          config.transformResponse?.(response);
        }
      });
    };
    console.log("streamAdapter Config:", config);
    const options: WechatMiniprogram.RequestOption = {
      method: (config?.method?.toUpperCase() ??
        "GET") as WechatMiniprogram.RequestOption["method"],
      url: buildURL(config.url, config.params, config.paramsSerializer),
      header: config.headers,
      data: config.data,
      responseType: "arraybuffer",
      enableChunked: true,
      complete: (response: any) => {
        requestTask.offChunkReceived(listener);
        settle(resolve, reject, {
          status: response.statusCode,
          errMsg: response.errMsg,
          headers: response.header,
          config,
          data: { data: chunkData.data, status: "done" } as StreamResponse,
        });
      },
    };
    requestTask = wx.request(options);
    requestTask.onChunkReceived(listener);

    // 新增 CancelToken 支持
    if (config.cancelToken) {
      config.cancelToken.promise.then(cancel => {
        requestTask.abort();
        reject(cancel);
      });
    }
  });
}
