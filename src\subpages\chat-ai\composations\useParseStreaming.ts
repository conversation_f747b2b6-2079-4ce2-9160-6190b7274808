import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 支持包含Markdown文本（可能包含换行符）的JSON对象。
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，可能带有"data:"前缀
 * @returns 解析后的JSON对象数组
 * @throws SyntaxError 如果输入字符串包含无效的JSON
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 匹配以"data:"开头的内容，并提取JSON部分
  const jsonPattern = /data:\s*(\{[\s\S]*?\})(?=(data:|$))/g;
  const matches = data.matchAll(jsonPattern);

  for (const match of matches) {
    let jsonStr = match[1].trim();
    if (jsonStr) {
      try {
        // 确保JSON字符串格式正确
        if (!jsonStr.startsWith("{")) {
          jsonStr = `{${jsonStr}`;
        }
        if (!jsonStr.endsWith("}")) {
          jsonStr = `${jsonStr}}`;
        }
        const parsed = JSON.parse(jsonStr);
        if (typeof parsed === "object" && parsed !== null) {
          parsedData.push(parsed);
        }
      } catch (error) {
        console.warn(
          `无法解析JSON字符串: ${jsonStr}。错误: ${(error as Error).message}`,
        );
        continue;
      }
    }
  }

  // 处理可能没有"data:"前缀的单JSON对象情况
  if (parsedData.length === 0) {
    const cleanData = data.replace(/data:/g, "").trim();
    try {
      const parsed = JSON.parse(cleanData);
      if (typeof parsed === "object" && parsed !== null) {
        if (Array.isArray(parsed)) {
          parsedData.push(...parsed);
        } else {
          parsedData.push(parsed);
        }
      }
    } catch (error) {
      console.warn(
        `无法解析JSON字符串: ${cleanData}。错误: ${(error as Error).message}`,
      );
    }
  }

  return parsedData;
}
