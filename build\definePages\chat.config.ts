import { defineUniPages } from "@uni-helper/vite-plugin-uni-pages";

export default defineUniPages({
  pages: [
    {
      path: "subpages/chat-ai/index",
      style: {
        navigationStyle: "custom",
        navigationBarTextStyle: "black",
        navigationBarTitleText: "小优",
        disableScroll: true,
      },
    },
  ],
  globalStyle: {
    navigationBarTextStyle: "black",
    navigationBarBackgroundColor: "#FFFFFF",
    usingComponents: {
      "merchant-popup":
        "wxcomponents/oig-weapp-components/merchant-popup/index",
    },
  },
});
