import type { ChatTypes } from "@/api";
import type { CancelTokenSource } from "axios";
import { FeedbackStatus, MessageStatusEnum, RoleEnum } from "@/enums/chat";
import { useChatStore } from "@/store/modules/chat";
import { isTempId } from "@/utils/is";
import { uniqueId } from "@/utils/shared";
import axios from "axios";
import dayjs from "dayjs";
import { TIP_TEXT } from "../constant";
import { useAgent } from "./useAgent";
import { useTransform } from "./useTransform";

export function useSession() {
  const { userInfo } = storeToRefs(useUserStore());
  const { messagesTransform } = useTransform();

  const chatStore = useChatStore();
  const {
    initLoading,
    setSessionId,
    clearMessages,
    addMessage,
    updateMessage,
    setSystemDefaultData,
  } = chatStore;
  const { sessionId, conversations, loading, messages, xyTips } =
    storeToRefs(chatStore);

  // 获取会话消息
  const source = ref<CancelTokenSource>();
  const querySessionMessages = async (session_id: string) => {
    if (isTempId(session_id)) {
      messages.value = [];
      return;
    }
    const res = await chatApi.fetchGetSessionInfo({
      session_id,
      user_id: userInfo.value?.userId,
    });
    console.log("querySessionMessages", res);

    if (res.messages) {
      messages.value = messagesTransform(res.messages);
      console.log("querySessionMessages", messages.value);
    }
  };

  const querySession = async ({ user_id }: ChatTypes.SessionReq) => {
    const res = await chatApi.fetchUserSession({ user_id });
    conversations.value = res.sessions || [];
  };
  const CancelToken = axios.CancelToken;
  source.value = CancelToken.source();

  const queryMessage = async ({
    user_id,
    prompt,
    attachment = [],
  }: ChatTypes.MessageReq) => {
    loading.value.chatTyping = true;
    attachment = attachment.filter(Boolean);

    addMessage({
      role: RoleEnum.user_message,
      content: prompt,
      status: MessageStatusEnum.Finish,
      file: attachment[0],
      feedback: FeedbackStatus.Normal,
      intention_id: 0,
    });

    const assistantMessage = addMessage({
      role: RoleEnum.Assistant,
      query: prompt,
      status: MessageStatusEnum.Loading,
      feedback: FeedbackStatus.Normal,
      intention_id: 0,
    });

    // 如果是临时会话ID，则不传session_id，服务端会创建新的会话
    const session_id = isTempId(sessionId.value) ? undefined : sessionId.value;
    const { agent } = useAgent();
    try {
      await chatApi.fetchMessage(
        {
          user_id: String(user_id),
          prompt,
          attachment,
          session_id: session_id && String(session_id),
        },
        ({ data, status }) => {
          console.log("data, status", data, status);
          agent(
            { data, status },
            {
              assistantMessageId: assistantMessage.id,
              onUpdate: updateMessage,
              onSuccess: (result: ChatTypes.IAgentMessage) => {
                loading.value.chatTyping = false;
                const lastMessage = messages.value[messages.value.length - 1];
                const conv = conversations.value[0];
                if (isTempId(conv.session_id) || !conv.session_id) {
                  conv.session_id = data?.message?.session_id || "";
                  lastMessage.intention_id = Number(
                    data?.message?.intention_id,
                  );
                  setSessionId(data?.message?.session_id || "");
                  // 刷新会话列表，获取最新的会话名
                  querySession({
                    user_id,
                  });
                }

                // 流式结束，更新消息状态为Finish
                updateMessage(assistantMessage.id, {
                  content: result?.content ?? "",
                  contentBlocks: result?.contentBlocks,
                  status: MessageStatusEnum.Finish,
                });
              },
              onError: (err: ChatTypes.IAgentError) => {
                loading.value.chatTyping = false;
                // 流式异常，更新消息状态为Error
                updateMessage(assistantMessage.id, {
                  content: err.message ?? "AI输出异常",
                  status: MessageStatusEnum.Error,
                });
              },
            },
          );
        },
        source.value?.token,
      );
    } catch (err) {
      console.log("err", err);
      loading.value.chatTyping = false;
      // 流式异常，更新消息状态为Error
      updateMessage(assistantMessage.id, {
        content: "AI输出异常",
        status: MessageStatusEnum.Error,
      });
    }
  };

  // 创建临时会话
  const createConversation = (session_name = "新会话") => {
    initLoading();

    // 查找是否已存在临时会话
    const tempConv = isTempId(conversations.value[0]?.session_id);
    if (tempConv) {
      clearMessages();
    } else {
      // 不存在则新建
      const session_id = `temp_${uniqueId()}`;
      const newConv: ChatTypes.Sessions = {
        group: "今天",
        session_id,
        session_name,
        created_time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };
      conversations.value.unshift(newConv);
      setSessionId(session_id);
      clearMessages();
    }
  };

  // 中断
  const cancelAgent = async ({ message_id }: ChatTypes.CancelAgentReq) => {
    await chatApi.fetchCancelAgent({ message_id });
  };

  const querySystemDefaultData = async () => {
    const rsp = await chatApi.fetchSystemDefaultData();
    setSystemDefaultData(rsp ?? {});
  };

  const queryGetXyTips = async ({ user_id }: ChatTypes.GetXyTipsRes) => {
    const rsp = await chatApi.fetchGetXyTips({ user_id: String(user_id) });
    xyTips.value = rsp;
  };

  const userInputPlaceholder = computed(() => {
    const conditions: Array<[boolean, string]> = [
      [loading.value.chatTyping, TIP_TEXT.ANSWER_WAIT],
      [loading.value.translating, TIP_TEXT.TRANSLATE_WAIT],
      [loading.value.userInputImageLoading, TIP_TEXT.FILE_WAIT],
    ];
    return (
      conditions.find(([condition]) => condition)?.[1] ||
      TIP_TEXT.QUERY_ERROR_EMPTY
    );
  });

  return {
    source,
    userInputPlaceholder,
    querySessionMessages,
    queryMessage,
    createConversation,
    cancelAgent,
    querySession,
    querySystemDefaultData,
    queryGetXyTips,
  };
}
