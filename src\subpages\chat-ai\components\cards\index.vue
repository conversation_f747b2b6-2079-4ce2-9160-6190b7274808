<template>
  <div class="animate-in rounded-24rpx bg-#F4F5F7 p-26rpx">
    <div class="mb-20rpx flex items-center justify-between">
      <div class="flex items-center">
        <image
          :src="cardIcon"
          mode="scaleToFill"
          class="mr-16rpx block h-48rpx w-48rpx"
        />
        <div class="h-40rpx text-28rpx c-#33373F font-500 lh-40rpx">
          {{ title }}
        </div>
      </div>
      <div
        class="h-36rpx text-26rpx c-#0591DB font-400 lh-36rpx"
        @click="handleJump('list')"
      >
        查看更多
      </div>
    </div>
    <slot name="default" />
    <!-- 查订单 -->
    <div class="list-container">
      <div
        v-if="cardType === CardTypeEnum.orderCard"
        class="list-wrap"
        :class="{ 'is-open': isOpen }"
      >
        <OrderItem
          v-for="(item, index) in curCardList"
          :key="`order-${index}`"
          :data="item"
          class="list-item"
          @click="handleJump('detail', item)"
        />
      </div>
      <!-- 查出库 -->
      <div
        v-else-if="cardType === CardTypeEnum.stockGoodsCard"
        class="list-wrap"
        :class="{ 'is-open': isOpen }"
      >
        <div class="flex text-28rpx font-400 leading-40rpx">
          <div
            v-for="(tab, index) in tabOptions"
            :key="tab.value"
            class="cursor-pointer rounded-8rpx px-20rpx py-10rpx transition-all"
            :class="[
              selectedTab === tab.value
                ? 'text-#1193DB bg-#1193DB bg-opacity-10'
                : 'text-#394253',
              index > 0 ? 'ml-20rpx' : '',
            ]"
            @click="handleTabChange(tab.value)"
          >
            {{ tab.label }}
          </div>
        </div>
        <StockItem
          v-for="(item, index) in curCardList"
          :key="`stock-${index}`"
          :data="item"
          class="list-item"
          @click="handleJump('detail', item)"
        />
      </div>
      <!-- 查清关进度 -->
      <div
        v-else-if="cardType === CardTypeEnum.clearanceCard"
        class="list-wrap"
        :class="{ 'is-open': isOpen }"
      >
        <ClearanceItem
          v-for="(item, index) in curCardList"
          :key="`clearance-${index}`"
          :data="item"
          class="list-item"
          @click="handleJump('detail', item)"
        />
      </div>
      <!-- 查船期 -->
      <div
        v-else-if="cardType === CardTypeEnum.shipTimeCard"
        class="list-wrap"
        :class="{ 'is-open': isOpen }"
      >
        <ShippingItem
          :data="curCardList"
          class="list-item"
          @click="item => handleJump('detail', item)"
        />
      </div>
      <!-- 查附件 -->
      <div
        v-else-if="cardType === CardTypeEnum.FILE"
        class="list-wrap"
        :class="{ 'is-open': isOpen }"
      >
        <FileItem :file="file" class="list-item" />
      </div>
    </div>

    <view class="mt-30rpx flex items-center justify-between">
      <view>
        <view
          v-if="cardType === CardTypeEnum.FILE"
          class="h-36rpx text-26rpx c-#0591DB font-400 lh-36rpx"
          @click="downloadAllFiles"
        >
          下载全部附件
        </view>
      </view>
      <view
        v-if="cardList.length > 2 && cardType !== CardTypeEnum.shipTimeCard"
        class="flex items-center"
        @click="handleCollapse"
      >
        <view class="h-36rpx text-26rpx c-#33373F font-400 lh-36rpx">
          {{ isOpen ? "收起" : "展开" }}
        </view>
        <image
          class="block h-40rpx w-40rpx"
          :style="{ transform: isOpen ? 'rotate(270deg)' : 'rotate(90deg)' }"
          :src="RightArrowIcon"
          mode="scaleToFill"
        />
      </view>
    </view>
  </div>
</template>

<script setup lang="ts">
import { Config } from "@/config";
import { CardTypeEnum } from "@/enums";
import ClearanceCardIcon from "@/static/chat/clearance-card.png";
import FileCardIcon from "@/static/chat/file-card.png";
import OrderCardIcon from "@/static/chat/order-card.png";
import ShippingCardIcon from "@/static/chat/shipping-card.png";
import StockCardIcon from "@/static/chat/stock-card.png";
import RightArrowIcon from "@/static/right-arrow.png";
import { ftPlugin } from "oig-finclip-jssdk/weapp";
import ClearanceItem from "./ClearanceItem.vue";
import FileItem from "./FileItem.vue";
import OrderItem from "./OrderItem.vue";
import ShippingItem from "./ShippingItem.vue";
import StockItem from "./StockItem.vue";

interface CardProps {
  cardType?: CardTypeEnum;
  cardList: any[];
  searchKey?: string;
}

interface ICardInfo {
  title?: string;
  listUrl?: string;
  h5ListUrl?: string;
  detailUrl?: string;
  h5DetailUrl?: string;
}

type IJumpInfo = Partial<Record<CardTypeEnum, ICardInfo>>;

const props = withDefaults(defineProps<CardProps>(), {
  cardType: CardTypeEnum.orderCard,
});

const { token } = useUserStore();
const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const isOpen = ref<boolean>(false);
function handleCollapse() {
  isOpen.value = !isOpen.value;
}

// 选项卡切换状态
const tabOptions = [
  { value: "goods", label: "按商品" },
  { value: "container", label: "按货柜" },
] as const;

const selectedTab = ref<"goods" | "container">("goods");
function handleTabChange(tab: "goods" | "container") {
  selectedTab.value = tab;
}

const curCardList = computed(() => {
  return isOpen.value ? props.cardList : props.cardList.slice(0, 2);
});

const file = ref({
  fileType: "xls",
  fileName: "库存汇总表 20250808",
});

function downloadAllFiles() {
  console.log("下载全部文件");
}

// 标题
const cardTitles = {
  orderCard: "我的订单",
  stockGoodsCard: "我的库存",
  clearanceCard: "清关进度",
  shipTimeCard: "我的船期",
  FILE: "附件",
};
// 图标
const iconObj = {
  orderCard: OrderCardIcon,
  stockGoodsCard: StockCardIcon,
  clearanceCard: ClearanceCardIcon,
  shipTimeCard: ShippingCardIcon,
  FILE: FileCardIcon,
};
const cardIcon = ref();
const title = ref<string>("");
const cardInfo = ref<ICardInfo>();
watch(
  () => props.cardType,
  val => {
    title.value = cardTitles[val];
    cardIcon.value = iconObj[val];
    const jumpInfo: IJumpInfo = {
      [CardTypeEnum.orderCard]: {
        title: "订单",
        listUrl: "/pages/order/index",
        detailUrl: "/pages/order/detail",
        h5ListUrl: "/proxy-h5/pages/order/index",
        h5DetailUrl: "/proxy-h5/pages/order/detail",
      },
      [CardTypeEnum.clearanceCard]: {
        title: "清关",
        listUrl: "/customs/index",
        detailUrl: "/customs/detail",
        h5ListUrl: "/proxy-h5/customs/index",
        h5DetailUrl: "/proxy-h5/customs/detail",
      },
      [CardTypeEnum.shipTimeCard]: {
        title: "船期",
        listUrl: "/shipmentDate/index",
        detailUrl: "/shipmentDate/detail",
        h5ListUrl: "/proxy-h5/shipmentDate/index",
        h5DetailUrl: "/proxy-h5/shipmentDate/detail",
      },
    };
    cardInfo.value = jumpInfo[props.cardType];
  },
  {
    immediate: true,
  },
);

// 跳转
function handleJump(type: "list" | "detail", item?: any) {
  let path = "";
  let h5Path = "";

  if (type === "list") {
    path = `${cardInfo.value?.listUrl}?searchKey=${props.searchKey}` || "";
    h5Path = cardInfo.value?.h5ListUrl || "";
  } else if (type === "detail") {
    path = `${cardInfo.value?.detailUrl}?orderId=${item.orderId}` || "";
    h5Path = cardInfo.value?.h5DetailUrl || "";
  }
  console.log("handleJump--path", path);
  console.log("handleJump--h5Path", h5Path);

  if (ftPlugin.inFinClip ?? false) {
    uni.navigateToMiniProgram({
      appId: Config.chatAppId,
      path,
      extraData: {
        [XHeader.HToken]: token,
        hiddenAppBarBack: "1",
      },
    });
  } else {
    openWebview({ title: cardInfo.value?.title, url: h5Path });
  }
}
</script>

<style lang="scss" scoped>
.animate-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.list-wrap {
  display: flex;
  flex-direction: column;
  gap: 20rpx 0;
  width: 100%;
  min-height: 0;
  max-height: 2000rpx; // 默认显示两个item的高度
  overflow: hidden;
  transition: max-height 0.3s ease-out;

  &.is-open {
    max-height: 4000rpx; // 展开时的最大高度，根据实际内容调整
  }
}
</style>
