<template>
  <div
    class="relative bg-[linear-gradient(180deg,rgba(255,255,255,0.18)_0%,#FFFFFF_17%,#FFFFFF_100%)]"
  >
    <!-- <div v-if="userInputImagePath">
      <ChatImage
        :url="userInputImagePath"
        :loading="loading.userInputImageLoading"
        class="mt-12rpx h-full w-full"
        @delete="handleDeleteUserInputImagePath"
      />
    </div> -->
    <div class="px-24rpx">
      <div
        class="border-2rpx border-#E8E8E8 rounded-40rpx border-solid px-22rpx pb-22rpx pt-22rpx shadow-[0_-0rpx_24rpx_0_rgba(178,178,178,0.29)]"
      >
        <div class="mb-34rpx h-full flex">
          <div class="mr-18rpx flex flex-col items-center justify-between">
            <div>
              <TextMoreBtn v-if="showMoreIcon" :rotate="270" />
            </div>
            <image
              :src="VoiceIcon"
              class="block self-start"
              :class="imageClass"
              @click="handleReset"
            />
          </div>
          <CustomInput
            v-model.trim="inputValue"
            class="flex-1"
            :placeholder="userInputPlaceholder"
            @linechange="onLinechange"
            @keyboardheightchange="event => emit('keyboardheightchange', event)"
          />
          <!-- <image
          :src="PictureIcon"
          :class="imageClass"
          @click="onUploadActionClick"
        /> -->
        </div>

        <div class="flex items-center justify-between">
          <div
            class="h-60rpx flex items-center border-1rpx border-#E0E0E0 rounded-30rpx border-solid pl-18rpx pr-10rpx"
            @click="showInstruct = true"
          >
            <image
              :src="instructIcon"
              mode="scaleToFill"
              class="mr-10rpx block h-40rpx w-40rpx"
            />
            <div
              class="border-r-1rpx border-r-#E0E0E0 border-r-solid pr-16rpx text-26rpx c-#33373F font-400 lh-60rpx"
            >
              常用命令
            </div>
            <image
              :src="ArrowLeftIcon"
              mode="scaleToFill"
              class="block h-40rpx w-40rpx"
              style="transform: rotate(90deg)"
            />
          </div>
          <!-- 发送按钮 -->
          <ChatSend />
        </div>
      </div>
    </div>
    <InstructPopup v-model="showInstruct" />
    <TextPopup v-model="showMoreText" />
    <nut-safe-area position="bottom" />
  </div>
</template>

<script setup lang="ts">
import instructIcon from "@/static/chat/instruct-icon.png";
import VoiceIcon from "@/static/chat/voice-icon.png";
import ArrowLeftIcon from "@/static/right-arrow.png";
import { useChatStore } from "@/store/modules/chat";
import { useSession } from "../composations/useSession";
import { TIP_TEXT } from "../constant";
import ChatSend from "./ChatSend.vue";
import CustomInput from "./CustomInput.vue";
import InstructPopup from "./InstructPopup.vue";
import TextMoreBtn from "./TextMoreBtn.vue";
import TextPopup from "./TextPopup.vue";

const emit = defineEmits<{
  (
    e: "keyboardheightchange",
    event:
      | UniHelper.InputOnKeyboardheightchangeEvent
      | UniHelper.TextareaOnKeyboardheightchangeEvent,
  ): void;
}>();

const { userInputPlaceholder } = useSession();
const chatStore = useChatStore();
const { clearMessages } = chatStore;
const { loading, messages, showMoreText, inputValue } = storeToRefs(
  useChatStore(),
);

const imageClass = "w-58rpx h-58rpx";
const { chooseImage, uploadSingleImage } = useUpload("app");
const userInputDisabled = computed(() => false);
const userInputImagePath = ref("");

// 是否显示指令弹窗
const showInstruct = ref(false);

// 是否显示更多文本按钮
const showMoreIcon = ref(false);
function onLinechange(event: UniHelper.TextareaOnLinechangeEvent) {
  const lineCount = event.detail?.lineCount;
  showMoreIcon.value = lineCount > 2;
}

async function onUploadActionClick() {
  if (userInputDisabled.value) {
    return showMsg(userInputPlaceholder.value);
  }
  if (userInputImagePath.value) {
    return showMsg(TIP_TEXT.FILE_ERROR_EXIST);
  }
  const chooseMediaResult = await chooseImage();
  loading.value.userInputImageLoading = true;
  try {
    const result = await uploadSingleImage(chooseMediaResult);
    userInputImagePath.value = result;
  } catch (error) {
    showMsg(error instanceof Error ? error.message : TIP_TEXT.DEFAULT_ERROR);
  } finally {
    loading.value.userInputImageLoading = false;
  }
}

function handleDeleteUserInputImagePath() {
  userInputImagePath.value = "";
}

function clear() {
  clearMessages();
  inputValue.value = "";
  userInputImagePath.value = "";
  loading.value.chatTyping = false;
  loading.value.userInputImageLoading = false;
  loading.value.translating = false;
  console.log(messages.value);
}

async function handleReset() {
  // await showConfirm("是否清空当前会话记录？");
  // await querySessionId(true);
  // clear();
}
</script>
