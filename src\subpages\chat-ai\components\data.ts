export const list: any[] = [
  {
    created_time: "2025-08-27 09:00:00",
    group: "今天",
    session_id: "1117",
    session_name: "查询订单4500123305",
    updated_time: "2025-08-27 09:00:00",
    user_id: 20034560,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-27 09:00:00",
    group: "今天",
    session_id: "1117",
    session_name: "查询订单4500123305",
    updated_time: "2025-08-27 09:00:00",
    user_id: 20034560,
  },
  {
    created_time: "2025-08-27 09:00:00",
    group: "今天",
    session_id: "1117",
    session_name: "查询订单4500123305",
    updated_time: "2025-08-27 09:00:00",
    user_id: 20034560,
  },
  {
    created_time: "2025-08-27 09:00:00",
    group: "今天",
    session_id: "1117",
    session_name: "查询订单4500123305",
    updated_time: "2025-08-27 09:00:00",
    user_id: 20034560,
  },
  {
    created_time: "2025-08-27 09:00:00",
    group: "今天",
    session_id: "1117",
    session_name: "查询订单4500123305",
    updated_time: "2025-08-27 09:00:00",
    user_id: 20034560,
  },
  {
    created_time: "2025-08-26 18:01:27",
    group: "昨天",
    session_id: 1115,
    session_name: "查询订单4500123303",
    updated_time: "2025-08-26 18:01:27",
    user_id: 20034558,
  },

  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 18:01:27",
    group: "昨天",
    session_id: 1115,
    session_name: "查询订单4500123303",
    updated_time: "2025-08-26 18:01:27",
    user_id: 20034558,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
  {
    created_time: "2025-08-26 19:00:00",
    group: "昨天",
    session_id: 1116,
    session_name: "查询订单4500123304",
    updated_time: "2025-08-26 19:00:00",
    user_id: 20034559,
  },
];

// {
//   warehouseName: "20",
//   grossWeight: 1900,
//   price: 6,
//   amount: 103238.3,
//   countryName: "新西兰",
//   factoryCode: "123",
//   goodsName: "白虾（去头），冷冻帝王蟹",
//   shelfEndExpDate: "2024-01-01",
//   declared: "beef breed:hostein,fine,beef beef  eef breed:hostein,fine",
// },
export const cardList = [
  {
    cabinetNo: "CAIU5522822",
    finalVoyageEta: "2024/12/25",
    portName: "上海（中国）",
    transferHarbourDate: "2024/11/11",
    transitHarbourName: "桑托斯（巴西）",
    contract: "R3373/2024",

    orderDetailList: [
      {
        id: 130109,
        sapItemId: 10,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000779",
        goodsName: "冷冻去骨牛霖（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2904.454",
        grossWeight: "3036.25",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 117,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "15248.38",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF EYEROUND",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130110,
        sapItemId: 20,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000802",
        goodsName: "冷冻去骨牛大米龙（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "5903.496",
        grossWeight: "6140.05",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 233,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "30993.35",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF KNUCKLE",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "15-35KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130111,
        sapItemId: 30,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000809",
        goodsName: "冷冻去骨牛小米龙（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "5815.794",
        grossWeight: "6060.5",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 245,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "30532.92",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF OUTSIDE FLAT",
        spScName: "",
        prodStartDate: "2024/09/09",
        prodEndDate: "2024/10/25",
        shelfStartExpDate: "2026/09/08",
        shelfEndExpDate: "2026/10/24",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "09/09/2024、11/09/2024、04/10/2024、09/10/2024、10/10/2024、16/10/2024、18/10/2024、21/10/2024、23/10/2024、25/10/2024",
        batchNum2: "",
        rangeValue: "13-33KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130112,
        sapItemId: 40,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000814",
        goodsName: "冷冻去骨牛针扒（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "7504.901",
        grossWeight: "7837.664",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 312,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "39400.73",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF TOPSIDE CAP OFF",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130113,
        sapItemId: 50,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000803",
        goodsName: "冷冻去骨牛后腱肉（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2388.268",
        grossWeight: "2493.9",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 99,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "12538.41",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF SHANK",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130114,
        sapItemId: 60,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000793",
        goodsName: "冷冻去骨牛龟腱（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2502.808",
        grossWeight: "2610.6",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 101,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "13139.74",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF HEEL MUSCLE",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
    ],
  },
  {
    cabinetNo: "CAIU5522822",
    finalVoyageEta: "2024/12/25",
    portName: "上海（中国）",
    transferHarbourDate: "2024/11/11",
    transitHarbourName: "桑托斯（巴西）",
    contract: "R3373/2024",

    orderDetailList: [
      {
        id: 130109,
        sapItemId: 10,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000779",
        goodsName: "冷冻去骨牛霖（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2904.454",
        grossWeight: "3036.25",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 117,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "15248.38",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF EYEROUND",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130110,
        sapItemId: 20,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000802",
        goodsName: "冷冻去骨牛大米龙（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "5903.496",
        grossWeight: "6140.05",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 233,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "30993.35",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF KNUCKLE",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "15-35KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130111,
        sapItemId: 30,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000809",
        goodsName: "冷冻去骨牛小米龙（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "5815.794",
        grossWeight: "6060.5",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 245,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "30532.92",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF OUTSIDE FLAT",
        spScName: "",
        prodStartDate: "2024/09/09",
        prodEndDate: "2024/10/25",
        shelfStartExpDate: "2026/09/08",
        shelfEndExpDate: "2026/10/24",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "09/09/2024、11/09/2024、04/10/2024、09/10/2024、10/10/2024、16/10/2024、18/10/2024、21/10/2024、23/10/2024、25/10/2024",
        batchNum2: "",
        rangeValue: "13-33KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130112,
        sapItemId: 40,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000814",
        goodsName: "冷冻去骨牛针扒（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "7504.901",
        grossWeight: "7837.664",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 312,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "39400.73",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF TOPSIDE CAP OFF",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130113,
        sapItemId: 50,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000803",
        goodsName: "冷冻去骨牛后腱肉（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2388.268",
        grossWeight: "2493.9",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 99,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "12538.41",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF SHANK",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130114,
        sapItemId: 60,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000793",
        goodsName: "冷冻去骨牛龟腱（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2502.808",
        grossWeight: "2610.6",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 101,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "13139.74",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF HEEL MUSCLE",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
    ],
  },
  {
    cabinetNo: "CAIU5522822",
    finalVoyageEta: "2024/12/25",
    portName: "上海（中国）",
    transferHarbourDate: "2024/11/11",
    transitHarbourName: "桑托斯（巴西）",
    contract: "R3373/2024",

    orderDetailList: [
      {
        id: 130109,
        sapItemId: 10,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000779",
        goodsName: "冷冻去骨牛霖（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2904.454",
        grossWeight: "3036.25",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 117,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "15248.38",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF EYEROUND",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130110,
        sapItemId: 20,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000802",
        goodsName: "冷冻去骨牛大米龙（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "5903.496",
        grossWeight: "6140.05",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 233,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "30993.35",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF KNUCKLE",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "15-35KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130111,
        sapItemId: 30,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000809",
        goodsName: "冷冻去骨牛小米龙（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "5815.794",
        grossWeight: "6060.5",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 245,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "30532.92",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF OUTSIDE FLAT",
        spScName: "",
        prodStartDate: "2024/09/09",
        prodEndDate: "2024/10/25",
        shelfStartExpDate: "2026/09/08",
        shelfEndExpDate: "2026/10/24",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "09/09/2024、11/09/2024、04/10/2024、09/10/2024、10/10/2024、16/10/2024、18/10/2024、21/10/2024、23/10/2024、25/10/2024",
        batchNum2: "",
        rangeValue: "13-33KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130112,
        sapItemId: 40,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000814",
        goodsName: "冷冻去骨牛针扒（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "7504.901",
        grossWeight: "7837.664",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 312,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "39400.73",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF TOPSIDE CAP OFF",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130113,
        sapItemId: 50,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000803",
        goodsName: "冷冻去骨牛后腱肉（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2388.268",
        grossWeight: "2493.9",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 99,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "12538.41",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF SHANK",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
      {
        id: 130114,
        sapItemId: 60,
        orderId: "**********",
        countryCode: "076",
        countryApplyCode: "BR",
        countryName: "巴西",
        goodsCode: "000000001000000793",
        goodsName: "冷冻去骨牛龟腱（30月龄以下）",
        hsCode: "**********",
        ciqCode: "**********102",
        factoryCode: "SIF4267",
        stockFactoryCode: "SIF4267",
        killFactoryCode: "SIF4267",
        weight: "2502.808",
        grossWeight: "2610.6",
        weightUnit: "KG",
        weightUnitName: "千克",
        pieceNum: 101,
        price: "5.25",
        priceDiffAmount: "0",
        amount: "13139.74",
        currency: "USD",
        spName: "",
        spEnName: "FROZEN BONELESS BEEF HEEL MUSCLE",
        spScName: "",
        prodStartDate: "2024/08/08",
        prodEndDate: "2024/09/16",
        shelfStartExpDate: "2026/08/07",
        shelfEndExpDate: "2026/09/15",
        shelfDays: 729,
        packType: "1",
        batchNum:
          "06/08/2024、07/08/2024、09/08/2024、12/08/2024、13/08/2024、14/08/2024、15/08/2024、19/08/2024、20/08/2024、21/08/2024、29/08/2024、30/08/2024、02/09/2024、04/09/2024、06/09/2024、09/09/2024、11/09/2024、13/09/2024",
        batchNum2: "",
        rangeValue: "14-34KG/抄码箱",
        healthCertificate: "",
      },
    ],
  },
];
