import { chatAppIdConfig } from "./appId";
import { baseUrlConfig, chatBaseUrlConfig, QiniuConfig } from "./url";

export class AuthConfig {
  static appId = "YDT10000";
  static channelId = "10000018";
  static clientId = "YXG";
  static runtime = "weapp";
  static productCode = "YDT10000";
}

export class Config {
  static get baseUrl() {
    return baseUrlConfig.current;
  }

  static get chatBaseUrl() {
    return chatBaseUrlConfig.current;
  }

  static get chatAppId() {
    return chatAppIdConfig.current;
  }

  static qiniuDownloadUrl = new QiniuConfig().current;
  static qiniuUploadUrl = "https://up-z2.qiniup.com";
  static merchantUrl = `${baseUrlConfig.current}/merchant-h5`;
}
